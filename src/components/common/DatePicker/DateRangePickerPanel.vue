<script setup lang="ts">
import { DatePanel, HeaderPanel, MonthPanel } from '@/components/common/DatePicker/panel'
import { useDateRangePickerPanel } from '@/components/common/DatePicker/DatePickerCore.ts'
import type { DateRangePickerEmits, HolidayMap } from './interface.ts'

const props = withDefaults(
  defineProps<{
    holidayMap?: HolidayMap
  }>(),
  {
    holidayMap: () => ({}),
  },
)
const emits = defineEmits<DateRangePickerEmits>()
const modalValue = defineModel<[number | null, number | null]>('modalValue', {
  default: () => [null, null],
})

const { leftPanel, rightPanel, core } = useDateRangePickerPanel(modalValue, emits)
</script>

<template>
  <div class="u-web-date-range-panel">
    <div class="panel">
      <HeaderPanel v-bind="leftPanel" />
      <DatePanel
        v-if="leftPanel.panelMode === 'date'"
        v-bind="{ ...leftPanel, ...core }"
        :values="modalValue"
      />
      <MonthPanel v-else-if="leftPanel.panelMode === 'month'" v-bind="leftPanel" />
    </div>
    <div class="panel">
      <HeaderPanel v-bind="rightPanel" />
      <DatePanel
        v-if="rightPanel.panelMode === 'date'"
        v-bind="{ ...rightPanel, ...core }"
        :values="modalValue"
      />
      <MonthPanel v-else-if="rightPanel.panelMode === 'month'" v-bind="rightPanel" />
    </div>
  </div>
</template>
