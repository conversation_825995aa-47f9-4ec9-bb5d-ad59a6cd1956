import type { ModelRef, Ref } from 'vue'
import { ref, computed, reactive, readonly, watch } from 'vue';
import type {
  DateCellMeta,
  DatePickerEmits,
  PanelModeType,
  DatePickerMonthOptions,
  DateRangePickerOptions,
  DateRangePickerEmits,
} from './interface.ts'
import dayjs, { type Dayjs } from 'dayjs'


function useMonthActions(panelMonth: Ref<Dayjs>,
                         options: DatePickerMonthOptions = {}) {
  // 面板模式与月份
  const panelMode = ref<PanelModeType>('date');

  // 月份切换
  const prevMonthDisable = computed(() => options.getPrevMonthDisable && options.getPrevMonthDisable());
  const nextMonthDisable = computed(() => options.getNextMonthDisable && options.getNextMonthDisable());

  // 面板当前月份
  function toPrevMonth() {
    if (prevMonthDisable.value) {
      return;
    }
    panelMonth.value =
      panelMode.value === 'date'
        ? panelMonth.value.subtract(1, 'month')
        : panelMonth.value.subtract(1, 'year');

    if (options.chanceMonth) {
      options.chanceMonth(panelMonth.value);
    }
  }
  function toNextMonth() {
    if (nextMonthDisable.value) {
      return;
    }
    panelMonth.value =
      panelMode.value === 'date' ? panelMonth.value.add(1, 'month') : panelMonth.value.add(1, 'year')
    if (options.chanceMonth) {
      options.chanceMonth(panelMonth.value);
    }
  }
  function toThisMonth() {
    panelMonth.value = dayjs().startOf('month')
    if (options.chanceMonth) {
      options.chanceMonth(panelMonth.value);
    }
  }

  function onSelectMonth(monthDayjs: Dayjs) {
    panelMonth.value = monthDayjs
    panelMode.value = 'date'
    if (options.chanceMonth) {
      options.chanceMonth(panelMonth.value);
    }
  }
  function onSetPanelMode(panelModeValue: PanelModeType) {
    console.log('onSelectMonth', panelModeValue)
    panelMode.value = panelModeValue;
    if (options.chanceMonth) {
      options.chanceMonth(panelMonth.value);
    }
  }
  return {
    panelMode: panelMode,
    prevMonthDisable,
    nextMonthDisable,
    toPrevMonth,
    toNextMonth,
    toThisMonth,
    onSelectMonth,
    onSetPanelMode,
  }
}

function useDateRangePickerSinglePanel(modelValue: Ref<number | null | undefined>,
                                       options: DateRangePickerOptions = {}) {
  // 面板当前月份
  const panelMonth = ref<Dayjs>((modelValue.value ? dayjs(modelValue.value) : dayjs()).startOf('month'));
  const monthActions = useMonthActions(panelMonth, options);
  // 这里使用 reactive 返回响应对象
  return {
    panelMonth,
    ...monthActions,
  }
}

export function useDateRangePickerPanel(modelValue: ModelRef<[number | null, number | null]>, emits: DateRangePickerEmits) {
  const start = ref<number | null>(
    modelValue.value[0] ?? dayjs().subtract(1, 'month').startOf('month').valueOf());
  const end = ref<number | null>(
    modelValue.value[1] ?? dayjs().startOf('month').valueOf());
  const leftPanel = useDateRangePickerSinglePanel(start, {
    getNextMonthDisable() {
      return leftPanel.panelMonth.value.diff(rightPanel.panelMonth.value, 'months') >= -1;
    },
    chanceMonth(monthDayjs) {
      if (leftPanel.panelMonth.value.diff(rightPanel.panelMonth.value, 'months') >= -1) {
        rightPanel.panelMonth.value = leftPanel.panelMonth.value.add(1, 'month')
      }
      emits('change-month', monthDayjs.year(), monthDayjs.month() + 1, 'left')
    }
  });
  const rightPanel = useDateRangePickerSinglePanel(end, {
    getPrevMonthDisable() {
      return rightPanel.panelMonth.value.diff(leftPanel.panelMonth.value, 'months') <= 1;
    },
    chanceMonth(monthDayjs) {
      if (rightPanel.panelMonth.value.diff(leftPanel.panelMonth.value, 'months') <= 1) {
        leftPanel.panelMonth.value = rightPanel.panelMonth.value.subtract(1, 'month');
      }
      emits('change-month', monthDayjs.year(), monthDayjs.month() + 1, 'right')
    }
  });
  watch(
    () => modelValue.value,
    (v) => {
      start.value = v?.[0] ?? null
      end.value = v?.[1] ?? null
    },
    { deep: true },
  )
  function onSelectCell(cell: DateCellMeta) {
    console.log("onSelectCell", cell, start.value, end.value)
    if (start.value != null && end.value == null) {
      end.value = cell.dateTimestamp
      if (start.value > end.value) {
        const t = start.value
        start.value = end.value
        end.value = t
      }
    } else {
      start.value = cell.dateTimestamp
      end.value = null
    }
    modelValue.value = [start.value, end.value];
  }
  return readonly(reactive({
    leftPanel,
    rightPanel,
    core: {
      onSelectCell,
    }
  }));
}
export function useDatePickerPanel(modelValue: ModelRef<number | undefined> | Ref<number | null | undefined>, emits: DatePickerEmits) {
  // 面板当前月份
  const panelMonth = ref<Dayjs>((modelValue.value ? dayjs(modelValue.value) : dayjs()).startOf('month'));
  const monthActions = useMonthActions(panelMonth, {
    chanceMonth: (monthDayjs) => {
      emits('change-month', monthDayjs.year(), monthDayjs.month() + 1)
    }
  });

  // [事件]选择日期
  function onSelectCell(cell: DateCellMeta) {
    modelValue.value = cell.dateTimestamp
    console.log('onSelectCell', cell, modelValue.value)
    if (!cell.inCurrentMonth) {
      panelMonth.value = dayjs(cell.dateTimestamp).startOf('month')
      emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
    }
  }

  // 这里使用 reactive 返回响应对象,
  return readonly(reactive({
    panelMonth: panelMonth,
    onSelectCell,
    ...monthActions,
  }))
}
