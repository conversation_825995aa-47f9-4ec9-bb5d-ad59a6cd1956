<script setup lang="ts">
import type { PanelProps, DatePickerEmits } from './interface.ts'
// @ts-ignore
import { DatePanel, HeaderPanel, MonthPanel } from '@/components/common/DatePicker/panel'
import { useDatePickerPanel } from './DatePickerCore.ts'
const props = withDefaults(defineProps<PanelProps>(), {
  size: 'default',
})
const emits = defineEmits<DatePickerEmits>()

const modelValue = defineModel<number>('modalValue')

const panel = useDatePickerPanel(modelValue, emits)
</script>

<template>
  <div class="u-web-date-panel" :class="[{ 'u-web-date-panel-small': size === 'small' }]">
    <HeaderPanel v-bind="panel" />
    <DatePanel v-if="panel.panelMode === 'date'"
               v-bind="panel"
               :value="modelValue" />
    <MonthPanel
      v-else-if="panel.panelMode === 'month'"
      v-bind="panel"
    />
  </div>
</template>

<style scoped lang="less">

</style>
