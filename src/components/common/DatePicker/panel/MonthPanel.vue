<script setup lang="ts">
import { computed } from 'vue'
import dayjs, { type Dayjs } from 'dayjs';

defineOptions({
  inheritAttrs: false
});

const props = defineProps<{
  panelMonth: Dayjs
}>()
const emits = defineEmits<{
  selectMonth: [Dayjs]
}>();
const today = dayjs();
const monthItems = computed(() =>
  Array.from({ length: 12 }, (_, i) => ({
    label: `${i + 1}月`,
    value: i,
  })),
)

function selectMonth(monthIndex: number) {
  const dayjs = props.panelMonth.month(monthIndex).startOf('month');
  emits('selectMonth', dayjs);
}
</script>

<template>
  <div class="u-web-date-panel-month-grid">
    <div
      v-for="m in monthItems"
      :key="m.value"
      class="u-web-date-panel-month-cell"
      :class="{
          active: panelMonth.month() === m.value,
          current: today.month() === m.value && today.year() === panelMonth.year(),
        }"
      @click="selectMonth(m.value)"
    >
      <span class="text">{{ m.label }}</span>
    </div>
  </div>
</template>
