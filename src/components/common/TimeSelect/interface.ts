export interface TimeSelectProps {
  /** 选中项绑定值 */
  modelValue?: string;
  /** 禁用状态 */
  disabled?: boolean;
  /** 文本框可输入 */
  editable?: boolean;
  /** 是否显示清除按钮 */
  clearable?: boolean;
  /** 是否在选项中包含end */
  includeEndTime?: boolean;
  /** 输入框尺寸 */
  size?: 'mini' | 'small' | 'medium' | 'large';
  /** 非范围选择时的占位内容 */
  placeholder?: string;
  /** 原生属性 */
  name?: string;
  /** 自定义前缀图标 */
  prefixIcon?: string;
  /** 自定义清除图标 */
  clearIcon?: string;
  /** 开始时间 */
  start?: string;
  /** 结束时间 */
  end?: string;
  /** 间隔时间 */
  step?: string;
  /** 最早时间点，早于该时间的时间段将被禁用 */
  minTime?: string;
  /** 最晚时间点，晚于该时间的时间段将被禁用 */
  maxTime?: string;
  /** 设置时间格式 */
  format?: string;
}

export interface TimeSelectEmits {
  /** 用户确认选定的值时触发 */
  change: [value: string];
  /** 在组件 Input 失去焦点时触发 */
  blur: [event: FocusEvent];
  /** 在组件 Input 获得焦点时触发 */
  focus: [event: FocusEvent];
  /** 可清空的单选模式下用户点击清空按钮时触发 */
  clear: [];
  /** 更新 modelValue */
  'update:modelValue': [value: string];
}

export interface TimeSelectInstance {
  /** 使 input 获取焦点 */
  focus: () => void;
  /** 使 input 失去焦点 */
  blur: () => void;
}

export interface TimeOption {
  value: string;
  label: string;
  disabled?: boolean;
}
